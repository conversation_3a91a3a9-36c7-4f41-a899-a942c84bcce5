# برنامج طباعة ملصقات مصرف الدم

## المتطلبات
- نظام تشغيل ويندوز 10 أو أحدث
- طابعة ملصقات متوافقة
- Python 3.10 أو أحدث (إذا لم تستخدم النسخة التنفيذية gui.exe)

## خطوات التثبيت والتشغيل

1. **تثبيت المتطلبات (إذا لم تستخدم النسخة التنفيذية):**
   - افتح نافذة الأوامر في مجلد البرنامج.
   - نفذ:
     ```
     pip install -r requirements.txt
     ```

2. **تشغيل البرنامج لأول مرة:**
   - إذا كان لديك ملف gui.exe:
     - شغّل الملف مباشرة بالنقر عليه مرتين.
   - إذا لم يكن لديك exe:
     - شغّل:
     ```
     python gui.py
     ```

3. **إعداد البرنامج:**
   - ستظهر لك نافذة إعدادات.
   - اختر اسم الطابعة من القائمة.
   - عدّل أبعاد الملصق وجودة الطباعة إذا لزم.
   - أدخل رابط الخادم ومفتاح API كما زودك به مسؤول النظام.
   - اضغط "حفظ الإعدادات".
   - اضغط "تشغيل الطابعة" لبدء الطباعة التلقائية.

4. **تشغيل البرنامج تلقائياً مع بدء تشغيل ويندوز:**
   - شغّل:
     ```
     python add_to_startup.py
     ```
   - أو إذا كان لديك exe:
     ```
     python add_to_startup.py gui.exe
     ```
   - سيعمل البرنامج تلقائياً عند كل إعادة تشغيل.

## ملاحظات هامة
- تأكد من وجود جميع الخطوط في مجلد fonts.
- إذا ظهرت رسالة خطأ عن الطابعة أو الخط، تحقق من الإعدادات أو تواصل مع الدعم.
- البرنامج يتصل تلقائياً بنظام إدارة التبرع بالدم (Laravel) ويطبع الملصقات فور وصول الطلبات.
- يمكنك إغلاق البرنامج من خلال إغلاق النافذة فقط.

## الدعم الفني
لأي استفسار أو مشكلة، تواصل مع مسؤول النظام أو الدعم الفني. 