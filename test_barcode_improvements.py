#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار تحسينات الباركود والمارجن العلوي
"""

import json
from label_design import create_compact_label_image
from datetime import datetime

def test_improved_barcode():
    """اختبار الباركود المحسن مع المارجن المقلل"""
    
    # تحميل الإعدادات المحدثة
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # بيانات اختبار
    lab_order_info = {
        'patient': {
            'name': 'أحمد محمد علي'
        },
        'created_at_raw': '2024-01-15T10:30:00Z'
    }
    
    sample_group = {
        'sample_code': 'LAB123456789',
        'name': 'Blood',
        'tests': [
            {'name': 'CBC'},
            {'name': 'ESR'},
            {'name': 'Blood Sugar'},
            {'name': 'Cholesterol'},
            {'name': 'Triglycerides'}
        ]
    }
    
    print("🔬 إنشاء ملصق اختبار مع الباركود المحسن...")
    
    # إنشاء الملصق
    label_image = create_compact_label_image(lab_order_info, sample_group, config)
    
    # حفظ الصورة للمراجعة
    output_path = 'test_improved_barcode_label.png'
    label_image.save(output_path)
    
    print(f"✅ تم حفظ الملصق في: {output_path}")
    print("\n📊 التحسينات المطبقة:")
    print(f"   • المارجن العلوي: {config['padding_mm']} مم (كان 0.5 مم)")
    print(f"   • ارتفاع الباركود: {config['barcode_height_mm']} مم (كان 7.0 مم)")
    print(f"   • عامل عرض الباركود: {config['barcode_width_factor']} (كان 1.3)")
    print(f"   • عرض وحدة الباركود: 0.28 مم (كان 0.33 مم)")
    print(f"   • المنطقة الهادئة: 0.2 مم (كان 0.3 مم)")
    
    return label_image

if __name__ == "__main__":
    test_improved_barcode()
