from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import json # Added for robust data parsing

# إعداد تسجيل الدخول الأساسي
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def start_server(log_queue, config, print_callback):
    app = Flask(__name__)
    CORS(app)  # السماح بطلبات Cross-Origin

    @app.route('/print-labels', methods=['POST'])
    def handle_print_request():
        log_queue.put("INFO: تم استلام طلب طباعة جديد...")
        
        # التحقق من أن نوع المحتوى هو JSON
        # if not request.is_json:
        #     log_queue.put("ERROR: الطلب المستلم ليس بصيغة JSON.")
        #     return jsonify({"status": "error", "message": "Invalid content type, expected application/json"}), 400

        raw_data = request.get_json()
        
        # --- Robust Data Parsing ---
        # للتعامل مع الحالة التي يتم فيها إرسال الـ JSON كنص داخل نص (stringified JSON)
        if isinstance(raw_data, str):
            try:
                data = json.loads(raw_data)
            except json.JSONDecodeError:
                log_queue.put("ERROR: البيانات المستلمة عبارة عن نص لا يمكن تحليله كـ JSON.")
                return jsonify({"status": "error", "message": "Received string is not valid JSON."}), 400
        else:
            data = raw_data
        
        # التحقق من صحة البيانات الأساسية
        if not isinstance(data, dict) or 'labOrder' not in data or 'sampleGroups' not in data:
            log_queue.put("ERROR: البيانات المستلمة غير مكتملة. الحقول 'labOrder' و 'sampleGroups' مطلوبة.")
            return jsonify({"status": "error", "message": "Missing required data: 'labOrder' and 'sampleGroups'"}), 400
        
        log_queue.put(f"INFO: طلب طباعة للمريض: {data.get('labOrder', {}).get('patient', {}).get('name', 'غير معروف')}")
        
        try:
            # استدعاء دالة الطباعة الفعلية وتمرير البيانات
            success = print_callback(data)
            if success:
                log_queue.put("INFO: تم إرسال مهمة الطباعة بنجاح.")
                return jsonify({"status": "success", "message": "Print job processed successfully"})
            else:
                log_queue.put("ERROR: فشلت عملية الطباعة. راجع السجل لمزيد من التفاصيل.")
                return jsonify({"status": "error", "message": "Printing failed"}), 500
        except Exception as e:
            log_queue.put(f"ERROR: حدث خطأ فادح أثناء الطباعة: {e}")
            logging.error(f"Critical error during printing: {e}", exc_info=True)
            return jsonify({"status": "error", "message": f"An internal error occurred: {e}"}), 500

    @app.route('/health-check', methods=['GET'])
    def health_check():
        return jsonify({"status": "ok", "message": "Server is running"})

    try:
        port = int(config.get('listening_port', 9898))
        log_queue.put(f"INFO: بدء تشغيل الخادم على المنفذ {port}...")
        # استخدام '0.0.0.0' لجعل الخادم متاحاً على الشبكة المحلية
        app.run(host='0.0.0.0', port=port)
    except Exception as e:
        log_queue.put(f"ERROR: فشل تشغيل الخادم: {e}")
        logging.error(f"Failed to start Flask server: {e}", exc_info=True)

if __name__ == '__main__':
    # هذا الجزء للاختبار المباشر للملف
    import queue
    test_queue = queue.Queue()
    test_config = {'listening_port': 9898}
    
    def dummy_print(data):
        print("--- DUMMY PRINT ---")
        print(f"Patient: {data['labOrder']['patient']['name']}")
        for group in data['sampleGroups']:
            print(f"  - Sample: {group['sample_code']} ({group['name']})")
        print("-------------------")
        # محاكاة النجاح
        return True

    # طباعة رسائل الاختبار من الطابور
    def log_consumer(q):
        while True:
            try:
                msg = q.get_nowait()
                print(f"LOG: {msg}")
            except queue.Empty:
                pass

    import threading
    threading.Thread(target=log_consumer, args=(test_queue,), daemon=True).start()

    start_server(test_queue, test_config, dummy_print) 